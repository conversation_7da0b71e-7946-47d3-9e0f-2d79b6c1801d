const supabase = require('../config/supabase');

class RestaurantService {
  async getAllRestaurants() {
    try {
      const { data, error } = await supabase
        .from('restaurants')
        .select('*')
        .eq('is_active', true)
        .order('rating', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching restaurants:', error);
      throw error;
    }
  }

  async getRestaurantInfo(restaurantId) {
    try {
      const { data, error } = await supabase
        .from('restaurants')
        .select('*')
        .eq('id', restaurantId)
        .eq('is_active', true)
        .single();

      if (error) throw error;
      if (!data) throw new Error('Restaurant not found');

      return data;
    } catch (error) {
      console.error('Error fetching restaurant info:', error);
      throw error;
    }
  }

  async searchRestaurants(query = "") {
    try {
      if (!query) {
        return await this.getAllRestaurants();
      }

      const { data, error } = await supabase
        .from('restaurants')
        .select('*')
        .eq('is_active', true)
        .ilike('name', `%${query}%`)
        .order('rating', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error searching restaurants:', error);
      throw error;
    }
  }

  async getMenuItems(restaurantId) {
    try {
      const { data, error } = await supabase
        .from('menu_items')
        .select('*')
        .eq('restaurant_id', restaurantId)
        .eq('is_available', true)
        .order('category', { ascending: true });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching menu items:', error);
      throw error;
    }
  }

  async getRestaurantWithMenu(restaurantId) {
    try {
      const restaurant = await this.getRestaurantInfo(restaurantId);
      const menuItems = await this.getMenuItems(restaurantId);

      return {
        ...restaurant,
        menu: menuItems
      };
    } catch (error) {
      console.error('Error fetching restaurant with menu:', error);
      throw error;
    }
  }

  async getFoodDeals() {
    try {
      // Get random menu items from each restaurant for deals
      const { data: restaurants, error: restaurantError } = await supabase
        .from('restaurants')
        .select('id, name')
        .eq('is_active', true);

      if (restaurantError) throw restaurantError;

      const deals = [];
      for (const restaurant of restaurants) {
        const { data: menuItems, error: menuError } = await supabase
          .from('menu_items')
          .select('*')
          .eq('restaurant_id', restaurant.id)
          .eq('is_available', true)
          .limit(1);

        if (!menuError && menuItems.length > 0) {
          const item = menuItems[0];
          deals.push({
            id: `deal-${restaurant.id}`,
            restaurant_id: restaurant.id,
            restaurant_name: restaurant.name,
            name: `Special ${item.name} Deal`,
            original_price: item.price,
            discounted_price: Math.floor(item.price * 0.8),
            description: `Get ${item.name} at 20% off!`,
            valid_until: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
            item_id: item.id
          });
        }
      }

      return deals;
    } catch (error) {
      console.error('Error fetching food deals:', error);
      throw error;
    }
  }

  async getMenuItemsByCategory(restaurantId) {
    try {
      const menuItems = await this.getMenuItems(restaurantId);
      const categorized = {};

      menuItems.forEach(item => {
        if (!categorized[item.category]) {
          categorized[item.category] = [];
        }
        categorized[item.category].push(item);
      });

      return categorized;
    } catch (error) {
      console.error('Error categorizing menu items:', error);
      throw error;
    }
  }
}

module.exports = new RestaurantService(); 