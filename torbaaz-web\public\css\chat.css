/* Chat Widget */
.chat-widget {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 350px;
    height: 500px;
    min-width: 300px;
    min-height: 400px;
    max-width: 90vw;
    max-height: 90vh;
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    transition: all 0.3s ease;
    overflow: visible;
    border: 2px solid #ff6b35;
    display: flex;
    flex-direction: column;
}

.chat-widget.dragging {
    transition: none;
    user-select: none;
}

.chat-widget.maximized {
    width: 90vw !important;
    height: 90vh !important;
    top: 5vh !important;
    left: 5vw !important;
    right: auto !important;
    bottom: auto !important;
}

.chat-widget.minimized {
    width: auto;
    height: auto;
    max-height: 60px;
    overflow: hidden;
}

.chat-widget.minimized .chat-content {
    display: none !important;
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease;
}

.chat-widget:not(.minimized) .chat-content {
    display: flex !important;
    opacity: 1 !important;
    transform: translateY(0) !important;
    transition: all 0.3s ease;
    visibility: visible !important;
}

.chat-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: calc(100% - 60px);
    min-height: 400px;
    background: white;
    border-radius: 0 0 15px 15px;
}

.chat-toggle {
    background: var(--primary-color);
    color: white;
    padding: 1rem;
    cursor: move;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 0.5rem;
    font-weight: 500;
    position: relative;
    height: 60px;
    box-sizing: border-box;
}

.chat-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.chat-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.chat-control-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.3s ease;
    font-size: 0.9rem;
}

.chat-control-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

.resize-handle {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 20px;
    height: 20px;
    background: linear-gradient(-45deg, transparent 0%, transparent 40%, #ff6b35 40%, #ff6b35 60%, transparent 60%);
    cursor: nw-resize;
    border-bottom-right-radius: 15px;
}

.chat-toggle i {
    font-size: 1.2rem;
}

.chat-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    min-height: 200px;
    background: #fafafa;
    border-radius: 8px;
    margin: 0.5rem;
}

.message {
    margin-bottom: 1rem;
    max-width: 80%;
    padding: 0.8rem 1rem;
    border-radius: 15px;
    position: relative;
    opacity: 1 !important;
    transform: translateY(0) !important;
    animation: slideIn 0.3s forwards;
}

.message.animate-in {
    opacity: 0;
    transform: translateY(20px);
    animation: slideIn 0.3s forwards;
}

.bot-message {
    background: #f5f5f5;
    margin-right: auto;
    border-radius: 15px 15px 15px 0;
}

.user-message {
    background: var(--primary-color);
    color: white;
    margin-left: auto;
    border-radius: 15px 15px 0 15px;
}

.suggestion-chips {
    padding: 1rem;
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    border-top: 1px solid #eee;
}

.suggestion-chip {
    background: #f5f5f5;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: background 0.3s ease;
}

.suggestion-chip:hover {
    background: #e0e0e0;
}

.chat-input {
    padding: 1rem;
    border-top: 1px solid #eee;
    display: flex;
    gap: 0.5rem;
}

.message-input {
    flex: 1;
    padding: 0.8rem;
    border: 1px solid #ddd;
    border-radius: 25px;
    resize: none;
    height: 45px;
    font-family: inherit;
    font-size: 0.9rem;
    line-height: 1.4;
}

.message-input:focus {
    outline: none;
    border-color: var(--primary-color);
}

.send-button {
    width: 45px;
    height: 45px;
    border: none;
    border-radius: 50%;
    background: var(--primary-color);
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.3s ease;
}

.send-button:hover {
    background: #e65100;
}

/* Animations */
@keyframes slideIn {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Loading Indicator */
.typing-indicator {
    display: flex;
    gap: 0.3rem;
    padding: 1rem;
    background: #f5f5f5;
    border-radius: 15px 15px 15px 0;
    width: fit-content;
}

.typing-indicator span {
    width: 8px;
    height: 8px;
    background: #999;
    border-radius: 50%;
    animation: typing 1s infinite;
}

.typing-indicator span:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes typing {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-5px);
    }
}

/* Message Highlights */
.highlight-price {
    color: #4CAF50;
    font-weight: bold;
}

.highlight-restaurant {
    color: var(--primary-color);
    font-weight: bold;
}

/* Responsive Design */
@media (max-width: 768px) {
    .chat-widget {
        width: 100%;
        height: 100%;
        bottom: 0;
        right: 0;
        border-radius: 0;
    }

    .chat-widget.minimized {
        width: auto;
        height: auto;
        bottom: 20px;
        right: 20px;
        border-radius: 15px;
    }

    .chat-content {
        height: calc(100% - 60px);
    }
} 