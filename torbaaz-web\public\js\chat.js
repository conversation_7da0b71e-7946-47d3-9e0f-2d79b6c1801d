// Chat widget functionality
class ChatWidget {
    constructor() {
        this.widget = document.querySelector('.chat-widget');
        this.container = document.querySelector('.chat-container');
        this.messagesContainer = document.querySelector('.chat-messages');
        this.input = document.querySelector('.message-input');
        this.sendButton = document.querySelector('.send-button');
        this.toggleButton = document.querySelector('.chat-toggle');
        this.suggestionChips = document.querySelector('.suggestion-chips');
        this.typingIndicator = document.createElement('div');
        this.typingIndicator.className = 'typing-indicator';
        
        // Generate a random user ID for this session
        this.userId = 'user_' + Math.random().toString(36).substring(2, 15);
        
        this.setupEventListeners();
        this.addWelcomeMessage();
    }

    setupEventListeners() {
        // Toggle chat widget
        this.toggleButton.addEventListener('click', () => {
            this.widget.classList.toggle('minimized');
        });

        // Send message on button click
        this.sendButton.addEventListener('click', () => this.sendMessage());

        // Send message on Enter key (but create new line on Shift+Enter)
        this.input.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });

        // Handle suggestion chip clicks
        this.suggestionChips.addEventListener('click', (e) => {
            if (e.target.classList.contains('suggestion-chip')) {
                this.input.value = e.target.textContent;
                this.sendMessage();
            }
        });
    }

    addWelcomeMessage() {
        this.addMessage("Hi! I'm Jarvis, your food assistant. How can I help you today?", 'bot');
        this.addSuggestionChips([
            "Show me today's menu",
            "Find me a restaurant",
            "What's on special today?",
            "Help me place an order"
        ]);
    }

    addSuggestionChips(chips) {
        this.suggestionChips.innerHTML = '';
        chips.forEach(chip => {
            const chipElement = document.createElement('div');
            chipElement.className = 'suggestion-chip';
            chipElement.textContent = chip;
            this.suggestionChips.appendChild(chipElement);
        });
    }

    addMessage(text, sender) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}-message`;

        // Process text for highlights
        text = this.processMessageText(text);

        messageDiv.innerHTML = text;
        this.container.appendChild(messageDiv);

        // Scroll to bottom
        this.scrollToBottom();
    }

    processMessageText(text) {
        // Highlight prices (Rs. format)
        text = text.replace(/Rs\.?\s*\d+(\.\d{2})?/g, match => `<span class="highlight-price">${match}</span>`);

        // Highlight restaurant names
        const restaurants = ['Pizza Point', 'Burger Lab', 'Chinese Wok', 'Desi Dhaba'];
        restaurants.forEach(restaurant => {
            const regex = new RegExp(restaurant, 'gi');
            text = text.replace(regex, match => `<span class="highlight-restaurant">${match}</span>`);
        });

        return text;
    }

    showTypingIndicator() {
        const indicator = document.createElement('div');
        indicator.className = 'typing-indicator';
        indicator.innerHTML = '<span></span><span></span><span></span>';
        this.container.appendChild(indicator);
        this.scrollToBottom();
    }

    hideTypingIndicator() {
        const indicator = this.container.querySelector('.typing-indicator');
        if (indicator) {
            indicator.remove();
        }
    }

    scrollToBottom() {
        this.container.scrollTop = this.container.scrollHeight;
    }

    async sendMessage() {
        const message = this.input.value.trim();
        if (!message) return;

        // Add user message to UI
        this.addMessage(message, 'user');
        
        // Clear input
        this.input.value = '';


        // Show typing indicator
        this.showTypingIndicator();

        try {
            // Send message to server
            const response = await fetch('/api/chat/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    userId: this.userId,
                    text: message
                })
            });

            if (!response.ok) {
                throw new Error('Failed to get response');
            }

            const data = await response.json();

            // Hide typing indicator
            this.hideTypingIndicator();

            // Add bot response to chat
            this.addMessage(data.reply, 'bot');

        } catch (error) {
            console.error('Chat error:', error);
            this.hideTypingIndicator();
            this.addMessage('Sorry, I encountered an error. Please try again.', 'bot');
        }
    }
}

// Initialize chat widget when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.chatWidget = new ChatWidget();
}); 