// Chat widget functionality
class ChatWidget {
    constructor() {
        this.widget = document.querySelector('.chat-widget');
        this.container = document.querySelector('.chat-container');

        // Create messages container if it doesn't exist
        let messagesContainer = document.querySelector('.chat-messages');
        if (!messagesContainer) {
            messagesContainer = document.createElement('div');
            messagesContainer.className = 'chat-messages';
            this.container.insertBefore(messagesContainer, this.container.firstChild);
        }
        this.messagesContainer = messagesContainer;

        this.input = document.querySelector('.message-input');
        this.sendButton = document.querySelector('.send-button');
        this.toggleButton = document.querySelector('.chat-toggle');
        this.suggestionChips = document.querySelector('.suggestion-chips');
        this.typingIndicator = document.createElement('div');
        this.typingIndicator.className = 'typing-indicator';

        // Generate a random user ID for this session
        this.userId = 'user_' + Math.random().toString(36).substring(2, 15);

        // Drag and resize properties
        this.isDragging = false;
        this.isResizing = false;
        this.dragOffset = { x: 0, y: 0 };
        this.isMaximized = false;
        this.originalPosition = { bottom: '20px', right: '20px', width: '350px', height: '500px' };

        this.setupEventListeners();
        this.setupDragAndResize();
        this.addWelcomeMessage();
    }

    setupEventListeners() {
        // Toggle chat widget - add click listener to the toggle button
        this.toggleButton.addEventListener('click', (e) => {
            // Don't toggle if clicking on control buttons
            if (!e.target.closest('.chat-control-btn') && !this.isDragging) {
                this.widget.classList.toggle('minimized');
            }
        });

        // Send message on button click
        this.sendButton.addEventListener('click', () => this.sendMessage());

        // Send message on Enter key (but create new line on Shift+Enter)
        this.input.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });

        // Handle suggestion chip clicks
        this.suggestionChips.addEventListener('click', (e) => {
            if (e.target.classList.contains('suggestion-chip')) {
                this.input.value = e.target.textContent;
                this.sendMessage();
            }
        });

        // Control buttons
        this.setupControlButtons();
    }

    setupControlButtons() {
        // Create control buttons if they don't exist
        let controls = this.toggleButton.querySelector('.chat-controls');
        if (!controls) {
            const titleDiv = document.createElement('div');
            titleDiv.className = 'chat-title';
            titleDiv.innerHTML = '<i class="fas fa-robot"></i><span>Ask Jarvis</span>';

            controls = document.createElement('div');
            controls.className = 'chat-controls';
            controls.innerHTML = `
                <button class="chat-control-btn maximize-btn" title="Maximize">
                    <i class="fas fa-expand"></i>
                </button>
                <button class="chat-control-btn minimize-btn" title="Minimize">
                    <i class="fas fa-minus"></i>
                </button>
            `;

            this.toggleButton.innerHTML = '';
            this.toggleButton.appendChild(titleDiv);
            this.toggleButton.appendChild(controls);
        }

        // Add event listeners for control buttons
        const maximizeBtn = controls.querySelector('.maximize-btn');
        const minimizeBtn = controls.querySelector('.minimize-btn');

        maximizeBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            this.toggleMaximize();
        });

        minimizeBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            this.widget.classList.add('minimized');
        });
    }

    addWelcomeMessage() {
        this.addMessage("Hi! I'm Jarvis, your food assistant. How can I help you today?", 'bot');
        this.addSuggestionChips([
            "Show me today's menu",
            "Find me a restaurant",
            "What's on special today?",
            "Help me place an order"
        ]);
    }

    addSuggestionChips(chips) {
        this.suggestionChips.innerHTML = '';
        chips.forEach(chip => {
            const chipElement = document.createElement('div');
            chipElement.className = 'suggestion-chip';
            chipElement.textContent = chip;
            this.suggestionChips.appendChild(chipElement);
        });
    }

    addMessage(text, sender) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}-message`;

        // Process text for highlights
        text = this.processMessageText(text);

        messageDiv.innerHTML = text;
        this.messagesContainer.appendChild(messageDiv);

        // Scroll to bottom
        this.scrollToBottom();
    }

    processMessageText(text) {
        // Highlight prices (Rs. format)
        text = text.replace(/Rs\.?\s*\d+(\.\d{2})?/g, match => `<span class="highlight-price">${match}</span>`);

        // Highlight restaurant names
        const restaurants = ['Pizza Point', 'Burger Lab', 'Chinese Wok', 'Desi Dhaba'];
        restaurants.forEach(restaurant => {
            const regex = new RegExp(restaurant, 'gi');
            text = text.replace(regex, match => `<span class="highlight-restaurant">${match}</span>`);
        });

        return text;
    }

    showTypingIndicator() {
        const indicator = document.createElement('div');
        indicator.className = 'typing-indicator';
        indicator.innerHTML = '<span></span><span></span><span></span>';
        this.messagesContainer.appendChild(indicator);
        this.scrollToBottom();
    }

    hideTypingIndicator() {
        const indicator = this.messagesContainer.querySelector('.typing-indicator');
        if (indicator) {
            indicator.remove();
        }
    }

    scrollToBottom() {
        this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
    }

    async sendMessage() {
        const message = this.input.value.trim();
        if (!message) return;

        // Add user message to UI
        this.addMessage(message, 'user');
        
        // Clear input
        this.input.value = '';


        // Show typing indicator
        this.showTypingIndicator();

        try {
            // Send message to server
            const response = await fetch('/api/chat/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    userId: this.userId,
                    text: message
                })
            });

            if (!response.ok) {
                throw new Error('Failed to get response');
            }

            const data = await response.json();

            // Hide typing indicator
            this.hideTypingIndicator();

            // Add bot response to chat
            this.addMessage(data.reply, 'bot');

        } catch (error) {
            console.error('Chat error:', error);
            this.hideTypingIndicator();
            this.addMessage('Sorry, I encountered an error. Please try again.', 'bot');
        }
    }

    setupDragAndResize() {
        // Add resize handle
        const resizeHandle = document.createElement('div');
        resizeHandle.className = 'resize-handle';
        this.widget.appendChild(resizeHandle);

        // Drag functionality
        this.toggleButton.addEventListener('mousedown', (e) => {
            if (e.target.closest('.chat-control-btn')) return;
            this.startDrag(e);
        });

        // Resize functionality
        resizeHandle.addEventListener('mousedown', (e) => {
            e.stopPropagation();
            this.startResize(e);
        });

        // Global mouse events
        document.addEventListener('mousemove', (e) => {
            if (this.isDragging) this.drag(e);
            if (this.isResizing) this.resize(e);
        });

        document.addEventListener('mouseup', () => {
            this.stopDrag();
            this.stopResize();
        });
    }

    startDrag(e) {
        if (this.widget.classList.contains('minimized')) return;

        this.isDragging = true;
        this.widget.classList.add('dragging');

        const rect = this.widget.getBoundingClientRect();
        this.dragOffset.x = e.clientX - rect.left;
        this.dragOffset.y = e.clientY - rect.top;

        // Convert to absolute positioning
        this.widget.style.position = 'fixed';
        this.widget.style.left = rect.left + 'px';
        this.widget.style.top = rect.top + 'px';
        this.widget.style.right = 'auto';
        this.widget.style.bottom = 'auto';
    }

    drag(e) {
        if (!this.isDragging) return;

        const newLeft = e.clientX - this.dragOffset.x;
        const newTop = e.clientY - this.dragOffset.y;

        // Keep widget within viewport
        const maxLeft = window.innerWidth - this.widget.offsetWidth;
        const maxTop = window.innerHeight - this.widget.offsetHeight;

        this.widget.style.left = Math.max(0, Math.min(newLeft, maxLeft)) + 'px';
        this.widget.style.top = Math.max(0, Math.min(newTop, maxTop)) + 'px';
    }

    stopDrag() {
        if (!this.isDragging) return;

        this.isDragging = false;
        this.widget.classList.remove('dragging');
    }

    startResize(e) {
        this.isResizing = true;
        this.resizeStartX = e.clientX;
        this.resizeStartY = e.clientY;
        this.resizeStartWidth = this.widget.offsetWidth;
        this.resizeStartHeight = this.widget.offsetHeight;
    }

    resize(e) {
        if (!this.isResizing) return;

        const deltaX = e.clientX - this.resizeStartX;
        const deltaY = e.clientY - this.resizeStartY;

        const newWidth = Math.max(300, this.resizeStartWidth + deltaX);
        const newHeight = Math.max(400, this.resizeStartHeight + deltaY);

        this.widget.style.width = newWidth + 'px';
        this.widget.style.height = newHeight + 'px';
    }

    stopResize() {
        this.isResizing = false;
    }

    toggleMaximize() {
        if (this.isMaximized) {
            // Restore to original size
            this.widget.classList.remove('maximized');
            this.widget.style.width = this.originalPosition.width;
            this.widget.style.height = this.originalPosition.height;
            this.widget.style.top = 'auto';
            this.widget.style.left = 'auto';
            this.widget.style.right = this.originalPosition.right;
            this.widget.style.bottom = this.originalPosition.bottom;

            const maximizeBtn = this.widget.querySelector('.maximize-btn i');
            maximizeBtn.className = 'fas fa-expand';
            this.isMaximized = false;
        } else {
            // Save current position
            this.originalPosition = {
                width: this.widget.style.width || '350px',
                height: this.widget.style.height || '500px',
                right: this.widget.style.right || '20px',
                bottom: this.widget.style.bottom || '20px'
            };

            // Maximize
            this.widget.classList.add('maximized');

            const maximizeBtn = this.widget.querySelector('.maximize-btn i');
            maximizeBtn.className = 'fas fa-compress';
            this.isMaximized = true;
        }
    }
}

// Initialize chat widget when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.chatWidget = new ChatWidget();
}); 