// Chat widget functionality
class ChatWidget {
    constructor() {
        console.log('🤖 Initializing Chat Widget...');

        // Find all required elements
        this.widget = document.querySelector('.chat-widget');
        this.container = document.querySelector('.chat-container');
        this.input = document.querySelector('.message-input');
        this.sendButton = document.querySelector('.send-button');
        this.toggleButton = document.querySelector('.chat-toggle');
        this.suggestionChips = document.querySelector('.suggestion-chips');

        if (!this.widget) {
            console.error('❌ Chat widget not found');
            return;
        }

        // Ensure messages container exists
        this.ensureMessagesContainer();

        // Generate a random user ID for this session
        this.userId = 'user_' + Math.random().toString(36).substring(2, 15);

        // Initialize components
        this.setupEventListeners();
        this.setupDragAndResize();

        // Force add welcome message immediately
        this.forceAddWelcomeMessage();

        console.log('✅ Chat widget initialized successfully');
    }

    ensureMessagesContainer() {
        // Find or create messages container
        let messagesContainer = document.querySelector('.chat-messages');

        if (!messagesContainer) {
            console.log('📝 Creating messages container...');
            messagesContainer = document.createElement('div');
            messagesContainer.className = 'chat-messages';
            messagesContainer.style.cssText = `
                flex: 1;
                overflow-y: auto;
                padding: 1rem;
                display: flex;
                flex-direction: column;
                gap: 0.5rem;
                min-height: 200px;
                background: #fafafa;
                border-radius: 8px;
                margin: 0.5rem;
            `;

            // Insert at the beginning of chat-container
            if (this.container) {
                this.container.insertBefore(messagesContainer, this.container.firstChild);
            } else {
                // Fallback: find chat-content and insert there
                const chatContent = document.querySelector('.chat-content');
                if (chatContent) {
                    chatContent.insertBefore(messagesContainer, chatContent.firstChild);
                }
            }
        }

        this.messagesContainer = messagesContainer;
        console.log('✅ Messages container ready:', this.messagesContainer);
    }

    setupEventListeners() {
        // Toggle chat widget - add click listener to the toggle button
        this.toggleButton.addEventListener('click', (e) => {
            // Don't toggle if clicking on control buttons
            if (!e.target.closest('.chat-control-btn') && !this.isDragging) {
                this.widget.classList.toggle('minimized');
            }
        });

        // Send message on button click
        this.sendButton.addEventListener('click', () => this.sendMessage());

        // Send message on Enter key (but create new line on Shift+Enter)
        this.input.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });

        // Handle suggestion chip clicks
        this.suggestionChips.addEventListener('click', (e) => {
            if (e.target.classList.contains('suggestion-chip')) {
                this.input.value = e.target.textContent;
                this.sendMessage();
            }
        });

        // Control buttons
        this.setupControlButtons();
    }

    setupControlButtons() {
        // Create control buttons if they don't exist
        let controls = this.toggleButton.querySelector('.chat-controls');
        if (!controls) {
            const titleDiv = document.createElement('div');
            titleDiv.className = 'chat-title';
            titleDiv.innerHTML = '<i class="fas fa-robot"></i><span>Ask Jarvis</span>';

            controls = document.createElement('div');
            controls.className = 'chat-controls';
            controls.innerHTML = `
                <button class="chat-control-btn maximize-btn" title="Maximize">
                    <i class="fas fa-expand"></i>
                </button>
                <button class="chat-control-btn minimize-btn" title="Minimize">
                    <i class="fas fa-minus"></i>
                </button>
            `;

            this.toggleButton.innerHTML = '';
            this.toggleButton.appendChild(titleDiv);
            this.toggleButton.appendChild(controls);
        }

        // Add event listeners for control buttons
        const maximizeBtn = controls.querySelector('.maximize-btn');
        const minimizeBtn = controls.querySelector('.minimize-btn');

        maximizeBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            this.toggleMaximize();
        });

        minimizeBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            this.widget.classList.add('minimized');
        });
    }

    forceAddWelcomeMessage() {
        console.log('🎉 Force adding welcome message...');

        // Ensure messages container exists
        if (!this.messagesContainer) {
            this.ensureMessagesContainer();
        }

        // Clear any existing messages
        if (this.messagesContainer) {
            this.messagesContainer.innerHTML = '';

            // Add welcome message directly with inline styles
            const welcomeMessage = document.createElement('div');
            welcomeMessage.className = 'message bot-message';
            welcomeMessage.style.cssText = `
                margin-bottom: 1rem;
                max-width: 80%;
                padding: 0.8rem 1rem;
                border-radius: 15px 15px 15px 0;
                background: #f5f5f5;
                margin-right: auto;
                opacity: 1;
                transform: translateY(0);
            `;
            welcomeMessage.innerHTML = "Hi! I'm Jarvis, your food assistant. 🍕 How can I help you today?";

            this.messagesContainer.appendChild(welcomeMessage);
            console.log('✅ Welcome message added directly');
        }

        // Add suggestion chips
        this.forceAddSuggestionChips([
            "Show me today's deals",
            "Find me a restaurant",
            "What's on special today?",
            "Help me place an order"
        ]);
    }

    addWelcomeMessage() {
        this.forceAddWelcomeMessage();
    }

    forceAddSuggestionChips(chips) {
        console.log('💡 Force adding suggestion chips...');

        // Find or create suggestion chips container
        let suggestionContainer = this.suggestionChips || document.querySelector('.suggestion-chips');

        if (!suggestionContainer) {
            console.log('📝 Creating suggestion chips container...');
            suggestionContainer = document.createElement('div');
            suggestionContainer.className = 'suggestion-chips';
            suggestionContainer.style.cssText = `
                padding: 1rem;
                display: flex;
                gap: 0.5rem;
                flex-wrap: wrap;
                border-top: 1px solid #eee;
            `;

            // Insert before chat-input
            const chatInput = document.querySelector('.chat-input');
            if (chatInput && chatInput.parentNode) {
                chatInput.parentNode.insertBefore(suggestionContainer, chatInput);
            }
        }

        suggestionContainer.innerHTML = '';

        chips.forEach(chip => {
            const chipElement = document.createElement('button');
            chipElement.className = 'suggestion-chip';
            chipElement.textContent = chip;
            chipElement.style.cssText = `
                background: #f5f5f5;
                border: none;
                padding: 0.5rem 1rem;
                border-radius: 20px;
                font-size: 0.9rem;
                cursor: pointer;
                transition: background 0.3s ease;
            `;

            chipElement.addEventListener('click', () => {
                if (this.input) {
                    this.input.value = chip;
                    this.sendMessage();
                }
            });

            chipElement.addEventListener('mouseover', () => {
                chipElement.style.background = '#e0e0e0';
            });

            chipElement.addEventListener('mouseout', () => {
                chipElement.style.background = '#f5f5f5';
            });

            suggestionContainer.appendChild(chipElement);
        });

        this.suggestionChips = suggestionContainer;
        console.log(`✅ Added ${chips.length} suggestion chips`);
    }

    addSuggestionChips(chips) {
        this.forceAddSuggestionChips(chips);
    }

    addMessage(text, sender) {
        if (!this.messagesContainer) {
            console.error('❌ Messages container not found');
            return;
        }

        console.log(`💬 Adding ${sender} message: ${text.substring(0, 50)}...`);

        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}-message`;

        // Process text for highlights
        text = this.processMessageText(text);

        messageDiv.innerHTML = text;
        this.messagesContainer.appendChild(messageDiv);

        // Scroll to bottom
        this.scrollToBottom();

        console.log('✅ Message added to chat');
    }

    processMessageText(text) {
        // Highlight prices (Rs. format)
        text = text.replace(/Rs\.?\s*\d+(\.\d{2})?/g, match => `<span class="highlight-price">${match}</span>`);

        // Highlight restaurant names
        const restaurants = ['Pizza Point', 'Burger Lab', 'Chinese Wok', 'Desi Dhaba'];
        restaurants.forEach(restaurant => {
            const regex = new RegExp(restaurant, 'gi');
            text = text.replace(regex, match => `<span class="highlight-restaurant">${match}</span>`);
        });

        return text;
    }

    showTypingIndicator() {
        const indicator = document.createElement('div');
        indicator.className = 'typing-indicator';
        indicator.innerHTML = '<span></span><span></span><span></span>';
        this.messagesContainer.appendChild(indicator);
        this.scrollToBottom();
    }

    hideTypingIndicator() {
        const indicator = this.messagesContainer.querySelector('.typing-indicator');
        if (indicator) {
            indicator.remove();
        }
    }

    scrollToBottom() {
        this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
    }

    async sendMessage() {
        const message = this.input.value.trim();
        if (!message) return;

        // Add user message to UI
        this.addMessage(message, 'user');
        
        // Clear input
        this.input.value = '';


        // Show typing indicator
        this.showTypingIndicator();

        try {
            // Send message to server
            const response = await fetch('/api/chat/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    userId: this.userId,
                    text: message
                })
            });

            if (!response.ok) {
                throw new Error('Failed to get response');
            }

            const data = await response.json();

            // Hide typing indicator
            this.hideTypingIndicator();

            // Add bot response to chat
            this.addMessage(data.reply, 'bot');

        } catch (error) {
            console.error('Chat error:', error);
            this.hideTypingIndicator();
            this.addMessage('Sorry, I encountered an error. Please try again.', 'bot');
        }
    }

    setupDragAndResize() {
        // Add resize handle
        const resizeHandle = document.createElement('div');
        resizeHandle.className = 'resize-handle';
        this.widget.appendChild(resizeHandle);

        // Drag functionality
        this.toggleButton.addEventListener('mousedown', (e) => {
            if (e.target.closest('.chat-control-btn')) return;
            this.startDrag(e);
        });

        // Resize functionality
        resizeHandle.addEventListener('mousedown', (e) => {
            e.stopPropagation();
            this.startResize(e);
        });

        // Global mouse events
        document.addEventListener('mousemove', (e) => {
            if (this.isDragging) this.drag(e);
            if (this.isResizing) this.resize(e);
        });

        document.addEventListener('mouseup', () => {
            this.stopDrag();
            this.stopResize();
        });
    }

    startDrag(e) {
        if (this.widget.classList.contains('minimized')) return;

        this.isDragging = true;
        this.widget.classList.add('dragging');

        const rect = this.widget.getBoundingClientRect();
        this.dragOffset.x = e.clientX - rect.left;
        this.dragOffset.y = e.clientY - rect.top;

        // Convert to absolute positioning
        this.widget.style.position = 'fixed';
        this.widget.style.left = rect.left + 'px';
        this.widget.style.top = rect.top + 'px';
        this.widget.style.right = 'auto';
        this.widget.style.bottom = 'auto';
    }

    drag(e) {
        if (!this.isDragging) return;

        const newLeft = e.clientX - this.dragOffset.x;
        const newTop = e.clientY - this.dragOffset.y;

        // Keep widget within viewport
        const maxLeft = window.innerWidth - this.widget.offsetWidth;
        const maxTop = window.innerHeight - this.widget.offsetHeight;

        this.widget.style.left = Math.max(0, Math.min(newLeft, maxLeft)) + 'px';
        this.widget.style.top = Math.max(0, Math.min(newTop, maxTop)) + 'px';
    }

    stopDrag() {
        if (!this.isDragging) return;

        this.isDragging = false;
        this.widget.classList.remove('dragging');
    }

    startResize(e) {
        this.isResizing = true;
        this.resizeStartX = e.clientX;
        this.resizeStartY = e.clientY;
        this.resizeStartWidth = this.widget.offsetWidth;
        this.resizeStartHeight = this.widget.offsetHeight;
    }

    resize(e) {
        if (!this.isResizing) return;

        const deltaX = e.clientX - this.resizeStartX;
        const deltaY = e.clientY - this.resizeStartY;

        const newWidth = Math.max(300, this.resizeStartWidth + deltaX);
        const newHeight = Math.max(400, this.resizeStartHeight + deltaY);

        this.widget.style.width = newWidth + 'px';
        this.widget.style.height = newHeight + 'px';
    }

    stopResize() {
        this.isResizing = false;
    }

    toggleMaximize() {
        if (this.isMaximized) {
            // Restore to original size
            this.widget.classList.remove('maximized');
            this.widget.style.width = this.originalPosition.width;
            this.widget.style.height = this.originalPosition.height;
            this.widget.style.top = 'auto';
            this.widget.style.left = 'auto';
            this.widget.style.right = this.originalPosition.right;
            this.widget.style.bottom = this.originalPosition.bottom;

            const maximizeBtn = this.widget.querySelector('.maximize-btn i');
            maximizeBtn.className = 'fas fa-expand';
            this.isMaximized = false;
        } else {
            // Save current position
            this.originalPosition = {
                width: this.widget.style.width || '350px',
                height: this.widget.style.height || '500px',
                right: this.widget.style.right || '20px',
                bottom: this.widget.style.bottom || '20px'
            };

            // Maximize
            this.widget.classList.add('maximized');

            const maximizeBtn = this.widget.querySelector('.maximize-btn i');
            maximizeBtn.className = 'fas fa-compress';
            this.isMaximized = true;
        }
    }
}

// Debug function to check chat widget status
function debugChatWidget() {
    console.log('🔍 Chat Widget Debug Info:');
    console.log('- Widget element:', document.querySelector('.chat-widget'));
    console.log('- Container element:', document.querySelector('.chat-container'));
    console.log('- Messages element:', document.querySelector('.chat-messages'));
    console.log('- Toggle element:', document.querySelector('.chat-toggle'));
    console.log('- Input element:', document.querySelector('.message-input'));
    console.log('- Send button:', document.querySelector('.send-button'));
    console.log('- Suggestion chips:', document.querySelector('.suggestion-chips'));
    console.log('- Chat widget instance:', window.chatWidget);
}

// Force chat widget initialization
function initializeChatWidget() {
    console.log('🚀 Force initializing chat widget...');

    // Wait for DOM to be ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                window.chatWidget = new ChatWidget();
                debugChatWidget();
            }, 200);
        });
    } else {
        // DOM is already ready
        setTimeout(() => {
            window.chatWidget = new ChatWidget();
            debugChatWidget();
        }, 200);
    }
}

// Initialize chat widget
initializeChatWidget();

// Also initialize on window load as backup
window.addEventListener('load', () => {
    if (!window.chatWidget) {
        console.log('🔄 Backup initialization...');
        window.chatWidget = new ChatWidget();
        debugChatWidget();
    }
});

// Manual test function - can be called from browser console
window.testChatWidget = function() {
    console.log('🧪 Manual Chat Widget Test...');

    // Force create a new chat widget
    window.chatWidget = new ChatWidget();

    // Force open the chat widget
    const widget = document.querySelector('.chat-widget');
    if (widget) {
        widget.classList.remove('minimized');
        console.log('✅ Chat widget opened');
    }

    // Test adding a message manually
    setTimeout(() => {
        if (window.chatWidget && window.chatWidget.messagesContainer) {
            const testMessage = document.createElement('div');
            testMessage.className = 'message bot-message';
            testMessage.style.cssText = `
                margin-bottom: 1rem;
                max-width: 80%;
                padding: 0.8rem 1rem;
                border-radius: 15px 15px 15px 0;
                background: #f5f5f5;
                margin-right: auto;
                opacity: 1;
                transform: translateY(0);
            `;
            testMessage.innerHTML = "🧪 Test message - Chat widget is working!";
            window.chatWidget.messagesContainer.appendChild(testMessage);
            console.log('✅ Test message added');
        }
    }, 500);

    console.log('🎯 Test complete. Check the chat widget!');
};