const { query<PERSON><PERSON><PERSON>, clearThread } = require('../services/assistantService');

class AssistantController {

  /**
   * Handle chat messages
   */
  async handleChat(req, res) {
    try {
      const { userId, text } = req.body;

      if (!userId || !text) {
        return res.status(400).json({
          error: 'Missing required fields: userId and text'
        });
      }

      const reply = await query<PERSON><PERSON><PERSON>(userId, text);
      res.json({ reply });

    } catch (error) {
      console.error('Chat handler error:', error);
      res.status(500).json({
        error: 'Failed to process chat message',
        details: error.message
      });
    }
  }

  /**
   * Clear chat history for a user
   */
  async clearChat(req, res) {
    try {
      const { userId } = req.body;

      if (!userId) {
        return res.status(400).json({
          error: 'Missing required field: userId'
        });
      }

      await clearThread(userId);
      res.json({ message: 'Chat history cleared successfully' });

    } catch (error) {
      console.error('Clear chat error:', error);
      res.status(500).json({
        error: 'Failed to clear chat history',
        details: error.message
      });
    }
  }
}

module.exports = new AssistantController(); 