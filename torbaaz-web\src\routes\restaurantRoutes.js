const express = require('express');
const router = express.Router();
const restaurantController = require('../controllers/restaurantController');

// Get all restaurants
router.get('/', restaurantController.getAllRestaurants);

// Search restaurants
router.get('/search', restaurantController.searchRestaurants);

// Get food deals
router.get('/deals', restaurantController.getFoodDeals);

// Get restaurant information
router.get('/:restaurantId', restaurantController.getRestaurantInfo);

// Get restaurant with full menu
router.get('/:restaurantId/full', restaurantController.getRestaurantWithMenu);

// Get restaurant menu items
router.get('/:restaurantId/menu', restaurantController.getMenuItems);

// Get restaurant menu items by category
router.get('/:restaurantId/menu/categories', restaurantController.getMenuItemsByCategory);

module.exports = router;