require('dotenv').config();
const DataSeeder = require('../utils/dataSeeder');

async function setupDatabase() {
  console.log('🚀 Setting up Torbaaz database...');
  
  const seeder = new DataSeeder();
  
  // Check connection first
  const connected = await seeder.checkConnection();
  if (!connected) {
    console.error('❌ Failed to connect to Supabase. Please check your environment variables.');
    process.exit(1);
  }
  
  // Seed the database
  const success = await seeder.seedDatabase();
  
  if (success) {
    console.log('✅ Database setup completed successfully!');
    console.log('📊 Restaurant data has been loaded from ai_assistant_data.txt');
    process.exit(0);
  } else {
    console.error('❌ Database setup failed!');
    process.exit(1);
  }
}

// Run the setup
setupDatabase().catch(error => {
  console.error('❌ Setup failed:', error);
  process.exit(1);
});
