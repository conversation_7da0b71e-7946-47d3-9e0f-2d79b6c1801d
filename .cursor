You are a senior full-stack developer AI assistant working on the "Jahanian Food Menu Web App" project. This application aims to aggregate menus from all restaurants in Jahanian, allowing users to browse, favorite, and order food items. An AI Assistant powered by OpenAI's Assistants API provides detailed information about food items, restaurant locations, and more.

**Project Objectives:**
- Utilize OpenAI's Assistants API with function calling.
- Implement at least one custom function/tool.
- Develop the application using Node.js (Express or Next.js preferred).
- Provide a minimal user interface or API endpoint for interaction.
- Host the project on a public GitHub repository with a detailed README.
- (Optional) Include a brief demo video or screenshots.
- Submit the project by Sunday, June 1, 8:00 PM.

**Code Structure and Best Practices:**
- Follow a modular architecture with separate directories for routes, controllers, services, and utilities.
- Use environment variables for configuration management.
- Implement error handling and input validation throughout the application.
- Write clean, readable, and well-documented code.
- Adhere to RESTful API design principles.

**OpenAI Integration:**
- Configure the OpenAI client using the API key stored in environment variables.
- Define custom functions (e.g., getRestaurantInfo) that can be invoked by the AI Assistant.
- Set up the Assistant with appropriate instructions and tools.
- Create endpoints to handle user queries and interact with the Assistant.

**Frontend Guidelines:**
- Develop a responsive and user-friendly interface.
- Allow users to view restaurant menus, favorite items, and place orders.
- Integrate a chat interface for AI Assistant interactions.

**Deployment:**
- Prepare the application for deployment on platforms like Heroku, Vercel, Netlify, or Firebase Hosting.
- Ensure all environment variables are set appropriately for the production environment.
- Optimize frontend assets for performance.

**Documentation:**
- Provide a comprehensive README.md file with:
  - Project overview
  - Installation and setup instructions
  - Usage guidelines
  - API endpoint details
  - Technologies used
  - Function calling implementation explanation

**Testing and Validation:**
- Test all API endpoints using tools like Postman or CURL.
- Validate the AI Assistant's responses for accuracy and relevance.
- Ensure the frontend interface functions as intended across different devices and browsers.

**Additional Notes:**
- Maintain version control using Git and commit changes regularly.
- Push the repository to GitHub and ensure it's public.
- Record a short demo video (1–2 minutes) showcasing the application's functionality and explain the code structure.
- do not run commands on power shell because command prompt is better

**Environment Variables:**
- OPENAI_API_KEY:********************************************************************************************************************************************************************
- OPENAI_ASSISTANT_ID: asst_uReDmCJ2C9fRZll33zD2Tb0K
- PORT: 3000
