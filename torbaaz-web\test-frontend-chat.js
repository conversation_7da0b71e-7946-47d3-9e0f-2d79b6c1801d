const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

async function testFrontendChatWidget() {
    try {
        console.log('🤖 Testing Frontend Chat Widget Integration...\n');
        
        // Test the exact endpoint the frontend uses
        console.log('Testing chat endpoint: /api/chat/chat');
        const response = await fetch('http://localhost:3000/api/chat/chat', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                userId: 'frontend_test_user',
                text: 'Hello Jarvis! Can you help me order food?'
            })
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const result = await response.json();
        console.log('✅ Chat Widget Response:', result.reply?.substring(0, 200) + '...');
        
        // Test another query
        console.log('\nTesting restaurant recommendation...');
        const response2 = await fetch('http://localhost:3000/api/chat/chat', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                userId: 'frontend_test_user',
                text: 'What restaurants do you recommend for dinner?'
            })
        });
        
        const result2 = await response2.json();
        console.log('✅ Restaurant Recommendation:', result2.reply?.substring(0, 200) + '...');
        
        console.log('\n🎉 Frontend Chat Widget is working perfectly!');
        console.log('✅ Endpoint: /api/chat/chat - WORKING');
        console.log('✅ JSON Response Format - CORRECT');
        console.log('✅ AI Assistant (Jarvis) - RESPONDING');
        
    } catch (error) {
        console.error('❌ Frontend Chat Widget test failed:', error.message);
    }
}

testFrontendChatWidget();
