const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

async function testChat() {
    try {
        console.log('Testing Torbaaz AI Chat (Jarvis)...\n');
        
        const testQueries = [
            "What restaurants do you have?",
            "I want pizza",
            "Show me menu for Khana Khazana",
            "What are today's deals?",
            "I want to order biryani",
            "Help me place an order"
        ];
        
        for (let i = 0; i < testQueries.length; i++) {
            const query = testQueries[i];
            console.log(`${i + 1}. Testing: "${query}"`);
            
            const response = await fetch('http://localhost:3000/api/chat/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    userId: `test_user_${i}`,
                    text: query
                })
            });
            
            const result = await response.json();
            console.log(`✅ Response: ${result.reply?.substring(0, 150)}...\n`);
            
            // Wait a bit between requests to avoid rate limiting
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
        
        console.log('🎉 All chat tests completed successfully!');
        
    } catch (error) {
        console.error('❌ Chat test failed:', error.message);
    }
}

testChat();
