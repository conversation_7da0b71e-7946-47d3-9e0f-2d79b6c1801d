// Global state
let currentFilter = 'all';
let allRestaurants = [];
let cart = [];
let currentUser = `user_${Date.now()}`;

// Initialize the application
document.addEventListener('DOMContentLoaded', async () => {
    console.log('🚀 Initializing Torbaaz Food Delivery App...');

    // Load initial data
    await Promise.all([
        loadRestaurants(),
        loadFoodDeals()
    ]);

    // Add event listeners
    setupEventListeners();

    // Add scroll animations
    setupScrollAnimations();

    console.log('✅ App initialized successfully!');
});

// Setup Event Listeners
function setupEventListeners() {
    // Search functionality
    const searchInput = document.querySelector('.search-bar input');
    if (searchInput) {
        searchInput.addEventListener('input', debounce(searchRestaurants, 300));
        searchInput.addEventListener('keypress', e => {
            if (e.key === 'Enter') searchRestaurants();
        });
    }

    // Search button
    const searchButton = document.querySelector('.search-bar button');
    if (searchButton) {
        searchButton.addEventListener('click', searchRestaurants);
    }

    // Category chips
    setupCategoryChips();
}

// Setup Scroll Animations
function setupScrollAnimations() {
    const observer = new IntersectionObserver(
        (entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-fade-in');
                }
            });
        },
        { threshold: 0.1 }
    );

    document.querySelectorAll('.restaurant-card, .deal-card').forEach(card => {
        observer.observe(card);
    });
}

// Setup Category Chips
function setupCategoryChips() {
    const categoryChips = document.querySelector('.category-chips');
    if (!categoryChips) return;

    const categories = [
        { name: 'All', icon: 'fas fa-utensils' },
        { name: 'Pizza', icon: 'fas fa-pizza-slice' },
        { name: 'Burgers', icon: 'fas fa-hamburger' },
        { name: 'Chinese', icon: 'fas fa-bowl-rice' },
        { name: 'Pakistani', icon: 'fas fa-pepper-hot' }
    ];

    categoryChips.innerHTML = categories.map(category => `
        <button class="category-chip ${category.name === 'All' ? 'active' : ''}"
                data-category="${category.name}">
            <i class="${category.icon}"></i>
            ${category.name}
        </button>
    `).join('');

    // Add event listeners to category chips
    categoryChips.addEventListener('click', (e) => {
        if (e.target.classList.contains('category-chip')) {
            document.querySelectorAll('.category-chip').forEach(chip =>
                chip.classList.remove('active')
            );
            e.target.classList.add('active');

            const category = e.target.dataset.category;
            filterRestaurants(category);
        }
    });
}

// Load Restaurants
async function loadRestaurants() {
    const restaurantsGrid = document.querySelector('.restaurants-grid');
    if (!restaurantsGrid) return;

    showLoading(restaurantsGrid);

    try {
        const response = await fetch('/api/restaurants');
        if (!response.ok) throw new Error('Failed to fetch restaurants');

        const restaurants = await response.json();
        allRestaurants = restaurants;
        displayRestaurants(restaurants);
    } catch (error) {
        console.error('Error loading restaurants:', error);
        showError(restaurantsGrid, 'Failed to load restaurants');
    }
}

// Load Food Deals
async function loadFoodDeals() {
    const dealsContainer = document.querySelector('.deals-container');
    if (!dealsContainer) return;

    showLoading(dealsContainer);

    try {
        const response = await fetch('/api/restaurants/deals');
        if (!response.ok) throw new Error('Failed to fetch deals');

        const deals = await response.json();
        displayDeals(deals);
    } catch (error) {
        console.error('Error loading deals:', error);
        showError(dealsContainer, 'Failed to load deals');
    }
}

// Display Restaurants
function displayRestaurants(restaurants) {
    const grid = document.querySelector('.restaurants-grid');
    if (!grid) return;

    grid.innerHTML = '';

    if (restaurants.length === 0) {
        showEmpty(grid, 'No restaurants found');
        return;
    }

    restaurants.forEach(restaurant => {
        const card = document.createElement('div');
        card.className = 'restaurant-card';
        card.innerHTML = `
            <div class="card-image">
                <img src="${restaurant.logo_url || '/assets/restaurant1.jpg'}"
                     alt="${restaurant.name}"
                     onerror="this.src='/assets/restaurant1.jpg'">
                <div class="rating">
                    <i class="fas fa-star"></i>
                    <span>${restaurant.rating}</span>
                </div>
            </div>
            <div class="card-content">
                <h3>${restaurant.name}</h3>
                <p class="cuisine">${restaurant.description}</p>
                <div class="address">
                    <i class="fas fa-clock"></i>
                    <span>${restaurant.delivery_time}</span>
                </div>
                <div class="card-footer">
                    <div class="contact">
                        <i class="fas fa-motorcycle"></i>
                        <span>Rs. ${restaurant.delivery_fee}</span>
                    </div>
                    <button class="view-menu" onclick="viewRestaurantMenu(${restaurant.id})">
                        View Menu
                    </button>
                </div>
            </div>
        `;
        grid.appendChild(card);
    });
}

// Display Deals
function displayDeals(deals) {
    const dealsContainer = document.querySelector('.deals-container');
    if (!dealsContainer) return;

    dealsContainer.innerHTML = '';

    if (deals.length === 0) {
        showEmpty(dealsContainer, 'No deals available');
        return;
    }

    deals.forEach((deal, index) => {
        const card = createDealCard(deal, index);
        dealsContainer.appendChild(card);
    });
}

// Create Deal Card
function createDealCard(deal, index) {
    const card = document.createElement('div');
    card.className = 'deal-card';
    card.style.animationDelay = `${index * 0.1}s`;

    const validUntil = new Date(deal.valid_until);
    const formattedDate = validUntil.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric'
    });

    const discount = Math.round(((deal.original_price - deal.discounted_price) / deal.original_price) * 100);

    card.innerHTML = `
        <div class="deal-image">
            <img src="/assets/restaurant1.jpg" alt="${deal.name}" onerror="this.src='/assets/restaurant1.jpg'">
            <div class="discount-badge">${discount}% OFF</div>
        </div>
        <div class="deal-content">
            <h3>${deal.name}</h3>
            <p class="restaurant">${deal.restaurant_name}</p>
            <p class="description">${deal.description}</p>
            <div class="price">
                <span class="original">Rs. ${deal.original_price}</span>
                <span class="discounted">Rs. ${deal.discounted_price}</span>
            </div>
            <button class="order-now" onclick="viewRestaurantMenu(${deal.restaurant_id})">
                Order Now
            </button>
        </div>
    `;

    return card;
}

// View Restaurant Menu
async function viewRestaurantMenu(restaurantId) {
    try {
        const response = await fetch(`/api/restaurants/${restaurantId}/full`);
        if (!response.ok) throw new Error('Failed to fetch restaurant details');

        const restaurant = await response.json();

        // Create modal or redirect to restaurant page
        showRestaurantModal(restaurant);
    } catch (error) {
        console.error('Error fetching restaurant details:', error);
        alert('Sorry, there was an error loading the restaurant menu.');
    }
}

// Show Restaurant Modal
function showRestaurantModal(restaurant) {
    // Create modal overlay
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h2>${restaurant.name}</h2>
                <button class="close-modal" onclick="closeModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="restaurant-info">
                    <img src="${restaurant.logo_url || '/assets/restaurant1.jpg'}" alt="${restaurant.name}">
                    <div class="info-details">
                        <p><i class="fas fa-star"></i> Rating: ${restaurant.rating}</p>
                        <p><i class="fas fa-clock"></i> Delivery: ${restaurant.delivery_time}</p>
                        <p><i class="fas fa-motorcycle"></i> Delivery Fee: Rs. ${restaurant.delivery_fee}</p>
                        <p><i class="fas fa-shopping-bag"></i> Minimum Order: Rs. ${restaurant.minimum_order}</p>
                    </div>
                </div>
                <div class="menu-section">
                    <h3>Menu</h3>
                    <div class="menu-items">
                        ${restaurant.menu ? restaurant.menu.map(item => `
                            <div class="menu-item">
                                <div class="item-info">
                                    <h4>${item.name}</h4>
                                    <p class="item-description">${item.description || ''}</p>
                                    <p class="item-category">${item.category}</p>
                                </div>
                                <div class="item-price">
                                    <span>Rs. ${item.price}</span>
                                    <button class="add-to-cart" onclick="addToCart(${item.id}, '${item.name}', ${item.price})">
                                        Add to Cart
                                    </button>
                                </div>
                            </div>
                        `).join('') : '<p>Menu not available</p>'}
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    document.body.style.overflow = 'hidden';
}

// Close Modal
function closeModal() {
    const modal = document.querySelector('.modal-overlay');
    if (modal) {
        modal.remove();
        document.body.style.overflow = 'auto';
    }
}

// Add to Cart
function addToCart(itemId, itemName, itemPrice) {
    const existingItem = cart.find(item => item.id === itemId);

    if (existingItem) {
        existingItem.quantity += 1;
    } else {
        cart.push({
            id: itemId,
            name: itemName,
            price: itemPrice,
            quantity: 1
        });
    }

    updateCartDisplay();
    showNotification(`${itemName} added to cart!`);
}

// Update Cart Display
function updateCartDisplay() {
    const cartCount = cart.reduce((total, item) => total + item.quantity, 0);
    const cartBadge = document.querySelector('.cart-badge');
    if (cartBadge) {
        cartBadge.textContent = cartCount;
        cartBadge.style.display = cartCount > 0 ? 'block' : 'none';
    }
}

// Show Notification
function showNotification(message) {
    const notification = document.createElement('div');
    notification.className = 'notification';
    notification.textContent = message;
    document.body.appendChild(notification);

    setTimeout(() => {
        notification.classList.add('show');
    }, 100);

    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => notification.remove(), 300);
    }, 3000);
}

// Filter Restaurants
function filterRestaurants(category) {
    currentFilter = category;

    let filteredRestaurants;
    if (category === 'All') {
        filteredRestaurants = allRestaurants;
    } else {
        // Filter based on restaurant name or menu items
        filteredRestaurants = allRestaurants.filter(restaurant => {
            return restaurant.name.toLowerCase().includes(category.toLowerCase()) ||
                   restaurant.description.toLowerCase().includes(category.toLowerCase());
        });
    }

    displayRestaurants(filteredRestaurants);
}

// Search Restaurants
async function searchRestaurants() {
    const searchInput = document.querySelector('.search-bar input');
    const query = searchInput.value.trim();
    const restaurantsGrid = document.querySelector('.restaurants-grid');

    if (!restaurantsGrid) return;

    showLoading(restaurantsGrid);

    try {
        const response = await fetch(`/api/restaurants/search?query=${encodeURIComponent(query)}`);
        if (!response.ok) throw new Error('Failed to fetch restaurants');

        const restaurants = await response.json();
        allRestaurants = restaurants;
        displayRestaurants(restaurants);
    } catch (error) {
        console.error('Error searching restaurants:', error);
        showError(restaurantsGrid, 'Failed to search restaurants');
    }
}

// Utility Functions
function showLoading(container) {
    container.innerHTML = `
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p>Loading...</p>
        </div>
    `;
}

function showError(container, message) {
    container.innerHTML = `
        <div class="error-state">
            <i class="fas fa-exclamation-circle"></i>
            <p>${message}</p>
            <button onclick="location.reload()" class="retry-btn">Try Again</button>
        </div>
    `;
}

function showEmpty(container, message) {
    container.innerHTML = `
        <div class="empty-state">
            <i class="fas fa-inbox"></i>
            <p>${message}</p>
        </div>
    `;
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Mobile Menu Toggle
function toggleMobileMenu() {
    const navLinks = document.querySelector('.nav-links');
    navLinks.classList.toggle('show');
}

// Shopping Cart Functions
function viewCart() {
    if (cart.length === 0) {
        showNotification('Your cart is empty!');
        return;
    }

    showCartModal();
}

function showCartModal() {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h2>Your Cart</h2>
                <button class="close-modal" onclick="closeModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="cart-items">
                    ${cart.map(item => `
                        <div class="cart-item">
                            <div class="item-details">
                                <h4>${item.name}</h4>
                                <p>Rs. ${item.price} each</p>
                            </div>
                            <div class="quantity-controls">
                                <button onclick="updateQuantity(${item.id}, ${item.quantity - 1})">-</button>
                                <span>${item.quantity}</span>
                                <button onclick="updateQuantity(${item.id}, ${item.quantity + 1})">+</button>
                            </div>
                            <div class="item-total">
                                Rs. ${item.price * item.quantity}
                            </div>
                            <button class="remove-item" onclick="removeFromCart(${item.id})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    `).join('')}
                </div>
                <div class="cart-summary">
                    <div class="total">
                        <strong>Total: Rs. ${getCartTotal()}</strong>
                    </div>
                    <button class="checkout-btn" onclick="showCheckoutForm()">
                        Proceed to Checkout
                    </button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    document.body.style.overflow = 'hidden';
}

function updateQuantity(itemId, newQuantity) {
    if (newQuantity <= 0) {
        removeFromCart(itemId);
        return;
    }

    const item = cart.find(item => item.id === itemId);
    if (item) {
        item.quantity = newQuantity;
        updateCartDisplay();
        // Refresh cart modal if open
        const modal = document.querySelector('.modal-overlay');
        if (modal) {
            closeModal();
            showCartModal();
        }
    }
}

function removeFromCart(itemId) {
    cart = cart.filter(item => item.id !== itemId);
    updateCartDisplay();
    showNotification('Item removed from cart');

    // Refresh cart modal if open
    const modal = document.querySelector('.modal-overlay');
    if (modal) {
        closeModal();
        if (cart.length > 0) {
            showCartModal();
        }
    }
}

function getCartTotal() {
    return cart.reduce((total, item) => total + (item.price * item.quantity), 0);
}

function showCheckoutForm() {
    closeModal();

    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h2>Checkout</h2>
                <button class="close-modal" onclick="closeModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="checkoutForm" onsubmit="placeOrder(event)">
                    <div class="form-group">
                        <label for="deliveryAddress">Delivery Address *</label>
                        <textarea id="deliveryAddress" required placeholder="Enter your complete address"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="phoneNumber">Phone Number *</label>
                        <input type="tel" id="phoneNumber" required placeholder="03XX-XXXXXXX">
                    </div>
                    <div class="form-group">
                        <label for="orderNotes">Special Instructions (Optional)</label>
                        <textarea id="orderNotes" placeholder="Any special requests..."></textarea>
                    </div>
                    <div class="order-summary">
                        <h3>Order Summary</h3>
                        ${cart.map(item => `
                            <div class="summary-item">
                                <span>${item.name} x ${item.quantity}</span>
                                <span>Rs. ${item.price * item.quantity}</span>
                            </div>
                        `).join('')}
                        <div class="summary-total">
                            <strong>Total: Rs. ${getCartTotal()}</strong>
                        </div>
                    </div>
                    <button type="submit" class="place-order-btn">Place Order</button>
                </form>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    document.body.style.overflow = 'hidden';
}

async function placeOrder(event) {
    event.preventDefault();

    const deliveryAddress = document.getElementById('deliveryAddress').value;
    const phoneNumber = document.getElementById('phoneNumber').value;
    const orderNotes = document.getElementById('orderNotes').value;

    if (!deliveryAddress || !phoneNumber) {
        showNotification('Please fill in all required fields');
        return;
    }

    // Get restaurant ID from the first item (assuming all items are from same restaurant)
    const restaurantId = cart[0].restaurantId || 1;

    const orderData = {
        userId: currentUser,
        restaurantId: restaurantId,
        items: cart.map(item => ({
            menu_item_id: item.id,
            quantity: item.quantity,
            price: item.price
        })),
        totalAmount: getCartTotal(),
        deliveryAddress: deliveryAddress,
        phoneNumber: phoneNumber,
        notes: orderNotes
    };

    try {
        const response = await fetch('/api/orders', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'x-user-id': currentUser
            },
            body: JSON.stringify(orderData)
        });

        if (!response.ok) throw new Error('Failed to place order');

        const order = await response.json();

        // Clear cart
        cart = [];
        updateCartDisplay();

        closeModal();
        showNotification('Order placed successfully! Order ID: ' + order.id);

    } catch (error) {
        console.error('Error placing order:', error);
        showNotification('Failed to place order. Please try again.');
    }
}

// Global click handler for modal close
document.addEventListener('click', (e) => {
    if (e.target.classList.contains('modal-overlay')) {
        closeModal();
    }
});

// Keyboard handler for modal close
document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape') {
        closeModal();
    }
});



