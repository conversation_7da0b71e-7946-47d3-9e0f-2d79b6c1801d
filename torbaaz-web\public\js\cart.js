// Cart page functionality
class CartManager {
    constructor() {
        this.cart = JSON.parse(localStorage.getItem('torbaaz_cart')) || [];
        this.promoCode = null;
        this.deliveryFee = 50;
        this.serviceFee = 25;
        this.discount = 0;
        
        this.init();
    }

    init() {
        this.updateCartDisplay();
        this.displayCartItems();
        this.loadRecommendedItems();
        this.setupEventListeners();
    }

    setupEventListeners() {
        // Update cart badge
        this.updateCartBadge();
        
        // Listen for storage changes (if cart is updated from other pages)
        window.addEventListener('storage', (e) => {
            if (e.key === 'torbaaz_cart') {
                this.cart = JSON.parse(e.newValue) || [];
                this.displayCartItems();
                this.updateCartDisplay();
            }
        });
    }

    displayCartItems() {
        const container = document.getElementById('cartItemsContainer');
        
        if (this.cart.length === 0) {
            container.innerHTML = `
                <div class="empty-cart">
                    <i class="fas fa-shopping-cart"></i>
                    <h3>Your cart is empty</h3>
                    <p>Add some delicious items to get started!</p>
                    <a href="index.html" class="browse-btn">Browse Restaurants</a>
                </div>
            `;
            this.updateCheckoutButton(false);
            return;
        }

        // Group items by restaurant
        const groupedItems = this.groupItemsByRestaurant();
        
        container.innerHTML = Object.keys(groupedItems).map(restaurantName => `
            <div class="restaurant-group">
                <div class="restaurant-header">
                    <h3><i class="fas fa-store"></i> ${restaurantName}</h3>
                    <span class="item-count">${groupedItems[restaurantName].length} items</span>
                </div>
                <div class="cart-items">
                    ${groupedItems[restaurantName].map(item => this.createCartItemHTML(item)).join('')}
                </div>
            </div>
        `).join('');

        this.updateCheckoutButton(true);
    }

    groupItemsByRestaurant() {
        const grouped = {};
        this.cart.forEach(item => {
            const restaurantName = item.restaurant_name || 'Unknown Restaurant';
            if (!grouped[restaurantName]) {
                grouped[restaurantName] = [];
            }
            grouped[restaurantName].push(item);
        });
        return grouped;
    }

    createCartItemHTML(item) {
        return `
            <div class="cart-item" data-item-id="${item.id}">
                <div class="item-image">
                    <img src="${item.image_url || '/images/placeholder.svg'}" 
                         alt="${item.name}" 
                         onerror="this.src='/images/placeholder.svg'">
                </div>
                <div class="item-details">
                    <h4>${item.name}</h4>
                    <p class="item-description">${item.description || ''}</p>
                    <div class="item-price">Rs. ${item.price}</div>
                </div>
                <div class="quantity-controls">
                    <button class="qty-btn minus" onclick="cartManager.updateQuantity('${item.id}', ${item.quantity - 1})">
                        <i class="fas fa-minus"></i>
                    </button>
                    <span class="quantity">${item.quantity}</span>
                    <button class="qty-btn plus" onclick="cartManager.updateQuantity('${item.id}', ${item.quantity + 1})">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
                <div class="item-total">
                    Rs. ${item.price * item.quantity}
                </div>
                <button class="remove-item" onclick="cartManager.removeItem('${item.id}')">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `;
    }

    updateQuantity(itemId, newQuantity) {
        if (newQuantity <= 0) {
            this.removeItem(itemId);
            return;
        }

        const itemIndex = this.cart.findIndex(item => item.id === itemId);
        if (itemIndex !== -1) {
            this.cart[itemIndex].quantity = newQuantity;
            this.saveCart();
            this.displayCartItems();
            this.updateCartDisplay();
            this.showNotification('Quantity updated');
        }
    }

    removeItem(itemId) {
        const itemIndex = this.cart.findIndex(item => item.id === itemId);
        if (itemIndex !== -1) {
            const itemName = this.cart[itemIndex].name;
            this.cart.splice(itemIndex, 1);
            this.saveCart();
            this.displayCartItems();
            this.updateCartDisplay();
            this.showNotification(`${itemName} removed from cart`);
        }
    }

    clearCart() {
        if (this.cart.length === 0) return;
        
        if (confirm('Are you sure you want to clear your cart?')) {
            this.cart = [];
            this.saveCart();
            this.displayCartItems();
            this.updateCartDisplay();
            this.showNotification('Cart cleared');
        }
    }

    updateCartDisplay() {
        const subtotal = this.getSubtotal();
        const total = this.getTotal();

        document.getElementById('subtotal').textContent = `Rs. ${subtotal}`;
        document.getElementById('total').textContent = `Rs. ${total}`;
        
        // Update discount display
        const discountRow = document.querySelector('.discount-row');
        if (this.discount > 0) {
            discountRow.style.display = 'flex';
            document.getElementById('discount').textContent = `-Rs. ${this.discount}`;
        } else {
            discountRow.style.display = 'none';
        }

        this.updateCartBadge();
    }

    updateCartBadge() {
        const cartBadge = document.querySelector('.cart-badge');
        const totalItems = this.cart.reduce((total, item) => total + item.quantity, 0);
        
        if (cartBadge) {
            cartBadge.textContent = totalItems;
            cartBadge.style.display = totalItems > 0 ? 'block' : 'none';
        }
    }

    updateCheckoutButton(enabled) {
        const checkoutBtn = document.querySelector('.checkout-btn');
        checkoutBtn.disabled = !enabled;
        
        if (enabled) {
            checkoutBtn.innerHTML = `
                <i class="fas fa-credit-card"></i>
                Proceed to Checkout (Rs. ${this.getTotal()})
            `;
        } else {
            checkoutBtn.innerHTML = `
                <i class="fas fa-credit-card"></i>
                Add items to checkout
            `;
        }
    }

    getSubtotal() {
        return this.cart.reduce((total, item) => total + (item.price * item.quantity), 0);
    }

    getTotal() {
        const subtotal = this.getSubtotal();
        if (subtotal === 0) return 0;
        
        return subtotal + this.deliveryFee + this.serviceFee - this.discount;
    }

    applyPromoCode() {
        const promoInput = document.getElementById('promoCode');
        const code = promoInput.value.trim().toUpperCase();
        
        if (!code) {
            this.showNotification('Please enter a promo code', 'error');
            return;
        }

        // Simulate promo code validation
        const promoCodes = {
            'SAVE10': { discount: 50, description: '10% off' },
            'FIRST20': { discount: 100, description: 'Rs. 100 off first order' },
            'WELCOME': { discount: 75, description: 'Welcome discount' }
        };

        if (promoCodes[code]) {
            this.discount = promoCodes[code].discount;
            this.promoCode = code;
            this.updateCartDisplay();
            this.showNotification(`Promo code applied: ${promoCodes[code].description}`);
            promoInput.value = '';
        } else {
            this.showNotification('Invalid promo code', 'error');
        }
    }

    async loadRecommendedItems() {
        try {
            // Get popular items or deals
            const response = await fetch('/api/restaurants/deals');
            const deals = await response.json();
            
            const recommendedContainer = document.getElementById('recommendedItems');
            const randomDeals = deals.sort(() => 0.5 - Math.random()).slice(0, 3);
            
            recommendedContainer.innerHTML = randomDeals.map(deal => `
                <div class="recommended-item" onclick="addRecommendedItem('${deal.id}')">
                    <img src="${deal.image_url || '/images/placeholder.svg'}" alt="${deal.name}">
                    <div class="recommended-info">
                        <h5>${deal.name}</h5>
                        <p class="recommended-price">Rs. ${deal.price}</p>
                    </div>
                    <button class="add-recommended">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
            `).join('');
            
        } catch (error) {
            console.error('Error loading recommended items:', error);
        }
    }

    addRecommendedItem(dealId) {
        // This would typically fetch the deal details and add to cart
        this.showNotification('Item added to cart!');
    }

    proceedToCheckout() {
        if (this.cart.length === 0) {
            this.showNotification('Your cart is empty', 'error');
            return;
        }

        // Store cart data for checkout
        localStorage.setItem('checkout_data', JSON.stringify({
            items: this.cart,
            subtotal: this.getSubtotal(),
            deliveryFee: this.deliveryFee,
            serviceFee: this.serviceFee,
            discount: this.discount,
            promoCode: this.promoCode,
            total: this.getTotal()
        }));

        // Redirect to checkout page (or show checkout modal)
        this.showCheckoutModal();
    }

    showCheckoutModal() {
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content checkout-modal">
                <div class="modal-header">
                    <h2>Checkout</h2>
                    <button class="close-modal" onclick="this.closest('.modal-overlay').remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="checkoutForm" onsubmit="cartManager.placeOrder(event)">
                        <div class="form-section">
                            <h3>Delivery Information</h3>
                            <div class="form-group">
                                <label for="customerName">Full Name *</label>
                                <input type="text" id="customerName" required>
                            </div>
                            <div class="form-group">
                                <label for="customerPhone">Phone Number *</label>
                                <input type="tel" id="customerPhone" required>
                            </div>
                            <div class="form-group">
                                <label for="deliveryAddress">Delivery Address *</label>
                                <textarea id="deliveryAddress" required rows="3"></textarea>
                            </div>
                        </div>
                        
                        <div class="form-section">
                            <h3>Payment Method</h3>
                            <div class="payment-options">
                                <label class="payment-option">
                                    <input type="radio" name="payment" value="cod" checked>
                                    <span>Cash on Delivery</span>
                                </label>
                                <label class="payment-option">
                                    <input type="radio" name="payment" value="card">
                                    <span>Credit/Debit Card</span>
                                </label>
                            </div>
                        </div>

                        <div class="order-summary-checkout">
                            <h3>Order Summary</h3>
                            <div class="summary-item">
                                <span>Subtotal:</span>
                                <span>Rs. ${this.getSubtotal()}</span>
                            </div>
                            <div class="summary-item">
                                <span>Delivery Fee:</span>
                                <span>Rs. ${this.deliveryFee}</span>
                            </div>
                            <div class="summary-item">
                                <span>Service Fee:</span>
                                <span>Rs. ${this.serviceFee}</span>
                            </div>
                            ${this.discount > 0 ? `
                                <div class="summary-item discount">
                                    <span>Discount:</span>
                                    <span>-Rs. ${this.discount}</span>
                                </div>
                            ` : ''}
                            <div class="summary-item total">
                                <span>Total:</span>
                                <span>Rs. ${this.getTotal()}</span>
                            </div>
                        </div>

                        <button type="submit" class="place-order-btn">
                            <i class="fas fa-check"></i>
                            Place Order (Rs. ${this.getTotal()})
                        </button>
                    </form>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
    }

    async placeOrder(event) {
        event.preventDefault();
        
        const formData = new FormData(event.target);
        const orderData = {
            customer_name: formData.get('customerName'),
            customer_phone: formData.get('customerPhone'),
            delivery_address: formData.get('deliveryAddress'),
            payment_method: formData.get('payment'),
            items: this.cart,
            subtotal: this.getSubtotal(),
            delivery_fee: this.deliveryFee,
            service_fee: this.serviceFee,
            discount: this.discount,
            promo_code: this.promoCode,
            total: this.getTotal()
        };

        try {
            // Simulate order placement
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            // Clear cart
            this.cart = [];
            this.saveCart();
            
            // Close modal
            document.querySelector('.modal-overlay').remove();
            
            // Show success message
            this.showNotification('Order placed successfully! You will receive a confirmation shortly.');
            
            // Redirect to orders page
            setTimeout(() => {
                window.location.href = 'orders.html';
            }, 2000);
            
        } catch (error) {
            console.error('Error placing order:', error);
            this.showNotification('Failed to place order. Please try again.', 'error');
        }
    }

    saveCart() {
        localStorage.setItem('torbaaz_cart', JSON.stringify(this.cart));
    }

    showNotification(message, type = 'success') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        document.body.appendChild(notification);

        setTimeout(() => notification.classList.add('show'), 100);
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }
}

// Global functions
function applyPromoCode() {
    cartManager.applyPromoCode();
}

function clearCart() {
    cartManager.clearCart();
}

function proceedToCheckout() {
    cartManager.proceedToCheckout();
}

function addRecommendedItem(dealId) {
    cartManager.addRecommendedItem(dealId);
}

// Initialize cart manager
let cartManager;
document.addEventListener('DOMContentLoaded', () => {
    cartManager = new CartManager();
});
