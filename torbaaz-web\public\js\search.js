// Advanced search functionality
class SearchEngine {
    constructor() {
        this.searchInput = document.querySelector('.search-bar input');
        this.searchButton = document.querySelector('.search-bar button');
        this.searchResults = null;
        this.searchData = {
            restaurants: [],
            menuItems: [],
            deals: []
        };
        
        this.setupSearchInterface();
        this.loadSearchData();
        this.setupEventListeners();
    }

    setupSearchInterface() {
        // Create search results container
        this.searchResults = document.createElement('div');
        this.searchResults.className = 'search-results';
        this.searchResults.style.display = 'none';
        
        // Insert after search bar
        const searchBar = document.querySelector('.search-bar');
        searchBar.parentNode.insertBefore(this.searchResults, searchBar.nextSibling);
        
        // Create search suggestions container
        this.searchSuggestions = document.createElement('div');
        this.searchSuggestions.className = 'search-suggestions';
        this.searchSuggestions.style.display = 'none';
        searchBar.appendChild(this.searchSuggestions);
    }

    setupEventListeners() {
        // Search input events
        this.searchInput.addEventListener('input', debounce((e) => {
            const query = e.target.value.trim();
            if (query.length >= 2) {
                this.showSuggestions(query);
            } else {
                this.hideSuggestions();
            }
        }, 300));

        this.searchInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                this.performSearch();
            }
            if (e.key === 'Escape') {
                this.hideSuggestions();
                this.hideResults();
            }
        });

        // Search button
        this.searchButton.addEventListener('click', () => {
            this.performSearch();
        });

        // Click outside to close
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.search-bar')) {
                this.hideSuggestions();
            }
        });

        // Focus events
        this.searchInput.addEventListener('focus', () => {
            const query = this.searchInput.value.trim();
            if (query.length >= 2) {
                this.showSuggestions(query);
            }
        });
    }

    async loadSearchData() {
        try {
            // Load restaurants
            const restaurantsResponse = await fetch('/api/restaurants');
            this.searchData.restaurants = await restaurantsResponse.json();

            // Load deals
            const dealsResponse = await fetch('/api/restaurants/deals');
            this.searchData.deals = await dealsResponse.json();

            // Load menu items from all restaurants
            this.searchData.menuItems = [];
            for (const restaurant of this.searchData.restaurants) {
                try {
                    const menuResponse = await fetch(`/api/restaurants/${restaurant.id}/menu`);
                    const menuItems = await menuResponse.json();
                    this.searchData.menuItems.push(...menuItems.map(item => ({
                        ...item,
                        restaurant_name: restaurant.name,
                        restaurant_id: restaurant.id
                    })));
                } catch (error) {
                    console.warn(`Failed to load menu for ${restaurant.name}`);
                }
            }

            console.log('Search data loaded:', this.searchData);
        } catch (error) {
            console.error('Error loading search data:', error);
        }
    }

    showSuggestions(query) {
        const suggestions = this.generateSuggestions(query);
        
        if (suggestions.length === 0) {
            this.hideSuggestions();
            return;
        }

        this.searchSuggestions.innerHTML = suggestions.map(suggestion => `
            <div class="suggestion-item" data-type="${suggestion.type}" data-query="${suggestion.query}">
                <i class="${suggestion.icon}"></i>
                <span>${suggestion.text}</span>
                <small>${suggestion.category}</small>
            </div>
        `).join('');

        // Add click listeners
        this.searchSuggestions.querySelectorAll('.suggestion-item').forEach(item => {
            item.addEventListener('click', () => {
                this.searchInput.value = item.dataset.query;
                this.performSearch();
                this.hideSuggestions();
            });
        });

        this.searchSuggestions.style.display = 'block';
    }

    generateSuggestions(query) {
        const suggestions = [];
        const lowerQuery = query.toLowerCase();

        // Restaurant suggestions
        this.searchData.restaurants.forEach(restaurant => {
            if (restaurant.name.toLowerCase().includes(lowerQuery)) {
                suggestions.push({
                    type: 'restaurant',
                    query: restaurant.name,
                    text: restaurant.name,
                    category: 'Restaurant',
                    icon: 'fas fa-store'
                });
            }
        });

        // Menu item suggestions
        this.searchData.menuItems.forEach(item => {
            if (item.name.toLowerCase().includes(lowerQuery)) {
                suggestions.push({
                    type: 'menu_item',
                    query: item.name,
                    text: `${item.name} at ${item.restaurant_name}`,
                    category: 'Menu Item',
                    icon: 'fas fa-utensils'
                });
            }
        });

        // Deal suggestions
        this.searchData.deals.forEach(deal => {
            if (deal.name.toLowerCase().includes(lowerQuery) || 
                deal.description.toLowerCase().includes(lowerQuery)) {
                suggestions.push({
                    type: 'deal',
                    query: deal.name,
                    text: deal.name,
                    category: 'Special Deal',
                    icon: 'fas fa-tags'
                });
            }
        });

        // Popular search suggestions
        const popularSearches = [
            { query: 'pizza deals', text: 'Pizza Deals', category: 'Popular', icon: 'fas fa-fire' },
            { query: 'biryani', text: 'Biryani', category: 'Popular', icon: 'fas fa-fire' },
            { query: 'burger offers', text: 'Burger Offers', category: 'Popular', icon: 'fas fa-fire' },
            { query: 'fast delivery', text: 'Fast Delivery', category: 'Popular', icon: 'fas fa-fire' }
        ];

        popularSearches.forEach(search => {
            if (search.query.includes(lowerQuery)) {
                suggestions.push({
                    type: 'popular',
                    query: search.query,
                    text: search.text,
                    category: search.category,
                    icon: search.icon
                });
            }
        });

        return suggestions.slice(0, 8); // Limit to 8 suggestions
    }

    async performSearch() {
        const query = this.searchInput.value.trim();
        if (!query) return;

        this.hideSuggestions();
        this.showSearchResults(query);

        // Perform the actual search
        const results = await this.searchAll(query);
        this.displaySearchResults(results, query);
    }

    async searchAll(query) {
        const lowerQuery = query.toLowerCase();
        const results = {
            restaurants: [],
            menuItems: [],
            deals: [],
            totalCount: 0
        };

        // Search restaurants
        results.restaurants = this.searchData.restaurants.filter(restaurant =>
            restaurant.name.toLowerCase().includes(lowerQuery) ||
            restaurant.description.toLowerCase().includes(lowerQuery) ||
            (restaurant.categories && restaurant.categories.some(cat => 
                cat.toLowerCase().includes(lowerQuery)
            ))
        );

        // Search menu items
        results.menuItems = this.searchData.menuItems.filter(item =>
            item.name.toLowerCase().includes(lowerQuery) ||
            item.description.toLowerCase().includes(lowerQuery)
        );

        // Search deals
        results.deals = this.searchData.deals.filter(deal =>
            deal.name.toLowerCase().includes(lowerQuery) ||
            deal.description.toLowerCase().includes(lowerQuery)
        );

        results.totalCount = results.restaurants.length + results.menuItems.length + results.deals.length;

        // Handle special searches
        if (lowerQuery.includes('pizza') && lowerQuery.includes('deal')) {
            // Redirect to deals page with pizza filter
            window.location.href = 'deals.html?category=pizza';
            return results;
        }

        if (lowerQuery.includes('deal') || lowerQuery.includes('offer')) {
            // Redirect to deals page
            window.location.href = 'deals.html';
            return results;
        }

        return results;
    }

    showSearchResults(query) {
        this.searchResults.innerHTML = `
            <div class="search-header">
                <h3>Search Results for "${query}"</h3>
                <button class="close-search" onclick="searchEngine.hideResults()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="search-content">
                <div class="loading-spinner">
                    <div class="spinner"></div>
                    <p>Searching...</p>
                </div>
            </div>
        `;
        this.searchResults.style.display = 'block';
    }

    displaySearchResults(results, query) {
        const content = this.searchResults.querySelector('.search-content');
        
        if (results.totalCount === 0) {
            content.innerHTML = `
                <div class="no-results">
                    <i class="fas fa-search"></i>
                    <h4>No results found for "${query}"</h4>
                    <p>Try searching for restaurants, food items, or deals</p>
                </div>
            `;
            return;
        }

        let html = '';

        // Restaurants section
        if (results.restaurants.length > 0) {
            html += `
                <div class="search-section">
                    <h4><i class="fas fa-store"></i> Restaurants (${results.restaurants.length})</h4>
                    <div class="search-items">
                        ${results.restaurants.map(restaurant => `
                            <div class="search-item restaurant-item" onclick="viewRestaurantMenu('${restaurant.id}')">
                                <img src="${restaurant.logo_url || '/images/placeholder.svg'}" alt="${restaurant.name}">
                                <div class="item-info">
                                    <h5>${restaurant.name}</h5>
                                    <p>${restaurant.description}</p>
                                    <div class="item-meta">
                                        <span><i class="fas fa-star"></i> ${restaurant.rating}</span>
                                        <span><i class="fas fa-clock"></i> ${restaurant.delivery_time || '25-35 mins'}</span>
                                    </div>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
        }

        // Menu items section
        if (results.menuItems.length > 0) {
            html += `
                <div class="search-section">
                    <h4><i class="fas fa-utensils"></i> Menu Items (${results.menuItems.length})</h4>
                    <div class="search-items">
                        ${results.menuItems.slice(0, 6).map(item => `
                            <div class="search-item menu-item" onclick="viewRestaurantMenu('${item.restaurant_id}')">
                                <img src="${item.image_url || '/images/placeholder.svg'}" alt="${item.name}">
                                <div class="item-info">
                                    <h5>${item.name}</h5>
                                    <p>at ${item.restaurant_name}</p>
                                    <div class="item-meta">
                                        <span class="price">Rs. ${item.price}</span>
                                    </div>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
        }

        // Deals section
        if (results.deals.length > 0) {
            html += `
                <div class="search-section">
                    <h4><i class="fas fa-tags"></i> Special Deals (${results.deals.length})</h4>
                    <div class="search-items">
                        ${results.deals.slice(0, 4).map(deal => `
                            <div class="search-item deal-item" onclick="window.location.href='deals.html'">
                                <img src="${deal.image_url || '/images/placeholder.svg'}" alt="${deal.name}">
                                <div class="item-info">
                                    <h5>${deal.name}</h5>
                                    <p>${deal.description}</p>
                                    <div class="item-meta">
                                        <span class="price">Rs. ${deal.price}</span>
                                        <span class="discount">Special Offer</span>
                                    </div>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
        }

        content.innerHTML = html;
    }

    hideSuggestions() {
        this.searchSuggestions.style.display = 'none';
    }

    hideResults() {
        this.searchResults.style.display = 'none';
    }
}

// Utility function
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Initialize search engine
let searchEngine;
document.addEventListener('DOMContentLoaded', () => {
    searchEngine = new SearchEngine();
});
