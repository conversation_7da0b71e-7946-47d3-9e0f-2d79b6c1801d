// Deals page functionality
let allDeals = [];
let filteredDeals = [];
let currentSort = 'discount';
let currentCategory = 'all';

// Initialize deals page
document.addEventListener('DOMContentLoaded', () => {
    loadDeals();
    setupEventListeners();
});

function setupEventListeners() {
    // Sort dropdown
    const sortSelect = document.getElementById('sortDeals');
    if (sortSelect) {
        sortSelect.addEventListener('change', (e) => {
            currentSort = e.target.value;
            sortAndDisplayDeals();
        });
    }

    // Category filters
    const filterButtons = document.querySelectorAll('.filter-btn');
    filterButtons.forEach(btn => {
        btn.addEventListener('click', (e) => {
            // Update active state
            filterButtons.forEach(b => b.classList.remove('active'));
            e.target.classList.add('active');
            
            currentCategory = e.target.dataset.category;
            filterAndDisplayDeals();
        });
    });

    // Search functionality
    const searchInput = document.querySelector('.search-bar input');
    if (searchInput) {
        searchInput.addEventListener('input', debounce(searchDeals, 300));
    }
}

async function loadDeals() {
    const dealsGrid = document.getElementById('dealsGrid');

    try {
        showLoading(dealsGrid);

        // Use sample deals with real images from assets folder
        allDeals = getSampleDeals();

        // Add discount calculations and categories
        allDeals = allDeals.map(deal => ({
            ...deal,
            discount_percentage: deal.original_price ?
                Math.round(((deal.original_price - deal.price) / deal.original_price) * 100) : 20,
            category: getCategoryFromDeal(deal),
            expires_in: getExpiryTime(deal.end_date)
        }));

        filteredDeals = [...allDeals];
        sortAndDisplayDeals();

    } catch (error) {
        console.error('Error loading deals:', error);
        showError(dealsGrid, 'Failed to load deals. Please try again.');
    }
}

function getSampleDeals() {
    return [
        {
            id: 'DEAL001',
            name: 'Pizza Slice Special',
            description: 'Delicious pizza slice with extra cheese and toppings',
            price: 250,
            original_price: 350,
            image_url: '/assets/images/pizzaslice1.jpg',
            restaurant_id: 'rest001',
            restaurants: { name: 'Crust Bros' },
            category: 'pizza',
            end_date: '2024-12-31'
        },
        {
            id: 'DEAL002',
            name: 'Chicken Biryani Deal',
            description: 'Authentic chicken biryani with raita and salad',
            price: 450,
            original_price: 600,
            image_url: '/assets/images/biryani.png',
            restaurant_id: 'rest002',
            restaurants: { name: 'Khana Khazana' },
            category: 'desi',
            end_date: '2024-12-31'
        },
        {
            id: 'DEAL003',
            name: 'Zinger Burger Combo',
            description: 'Crispy zinger burger with fries and drink',
            price: 380,
            original_price: 500,
            image_url: '/assets/images/zinger.png',
            restaurant_id: 'rest003',
            restaurants: { name: 'Eatway' },
            category: 'burger',
            end_date: '2024-12-31'
        },
        {
            id: 'DEAL004',
            name: 'Chinese Noodles Special',
            description: 'Spicy chicken noodles with vegetables',
            price: 320,
            original_price: 450,
            image_url: '/assets/images/noodles.png',
            restaurant_id: 'rest004',
            restaurants: { name: 'Meet N Eat' },
            category: 'chinese',
            end_date: '2024-12-31'
        },
        {
            id: 'DEAL005',
            name: 'Chicken Shawarma',
            description: 'Fresh chicken shawarma with garlic sauce',
            price: 200,
            original_price: 280,
            image_url: '/assets/images/shawarma.png',
            restaurant_id: 'rest005',
            restaurants: { name: 'MFC' },
            category: 'other',
            end_date: '2024-12-31'
        },
        {
            id: 'DEAL006',
            name: 'Pasta Special',
            description: 'Creamy white sauce pasta with chicken',
            price: 350,
            original_price: 480,
            image_url: '/assets/images/pasta.png',
            restaurant_id: 'rest006',
            restaurants: { name: 'Pizza Corner' },
            category: 'other',
            end_date: '2024-12-31'
        }
    ];
}

function getCategoryFromDeal(deal) {
    const name = deal.name.toLowerCase();
    const description = deal.description.toLowerCase();
    
    if (name.includes('pizza') || description.includes('pizza')) return 'pizza';
    if (name.includes('burger') || description.includes('burger')) return 'burger';
    if (name.includes('biryani') || name.includes('karahi') || description.includes('desi')) return 'desi';
    if (name.includes('chinese') || description.includes('chinese')) return 'chinese';
    
    return 'other';
}

function getExpiryTime(endDate) {
    if (!endDate) return 'Limited time';
    
    const now = new Date();
    const end = new Date(endDate);
    const diffHours = Math.ceil((end - now) / (1000 * 60 * 60));
    
    if (diffHours < 24) return `${diffHours} hours left`;
    if (diffHours < 168) return `${Math.ceil(diffHours / 24)} days left`;
    
    return 'Limited time';
}

function filterAndDisplayDeals() {
    if (currentCategory === 'all') {
        filteredDeals = [...allDeals];
    } else {
        filteredDeals = allDeals.filter(deal => deal.category === currentCategory);
    }
    
    sortAndDisplayDeals();
}

function sortAndDisplayDeals() {
    // Sort deals based on current sort option
    switch (currentSort) {
        case 'discount':
            filteredDeals.sort((a, b) => (b.discount_percentage || 0) - (a.discount_percentage || 0));
            break;
        case 'price':
            filteredDeals.sort((a, b) => a.price - b.price);
            break;
        case 'rating':
            filteredDeals.sort((a, b) => (b.restaurants?.rating || 0) - (a.restaurants?.rating || 0));
            break;
        case 'expiry':
            filteredDeals.sort((a, b) => {
                const aHours = getExpiryHours(a.end_date);
                const bHours = getExpiryHours(b.end_date);
                return aHours - bHours;
            });
            break;
    }
    
    displayDeals(filteredDeals);
}

function getExpiryHours(endDate) {
    if (!endDate) return 999999; // Put unlimited deals at the end
    const now = new Date();
    const end = new Date(endDate);
    return Math.ceil((end - now) / (1000 * 60 * 60));
}

function displayDeals(deals) {
    const dealsGrid = document.getElementById('dealsGrid');
    
    if (deals.length === 0) {
        showEmpty(dealsGrid, 'No deals found for the selected category.');
        return;
    }
    
    dealsGrid.innerHTML = deals.map(deal => createDealCard(deal)).join('');
}

function createDealCard(deal) {
    const discountBadge = deal.discount_percentage > 0 ? 
        `<div class="discount-badge">${deal.discount_percentage}% OFF</div>` : '';
    
    const originalPrice = deal.original_price ? 
        `<span class="original-price">Rs. ${deal.original_price}</span>` : '';
    
    return `
        <div class="deal-card" data-deal-id="${deal.id}">
            ${discountBadge}
            <div class="deal-image">
                <img src="${deal.image_url || '/images/placeholder.svg'}" 
                     alt="${deal.name}" 
                     onerror="this.src='/images/placeholder.svg'">
                <div class="deal-timer">${deal.expires_in}</div>
            </div>
            <div class="deal-content">
                <div class="deal-header">
                    <h3>${deal.name}</h3>
                    <div class="restaurant-info">
                        <i class="fas fa-store"></i>
                        <span>${deal.restaurants?.name || 'Restaurant'}</span>
                    </div>
                </div>
                <p class="deal-description">${deal.description}</p>
                <div class="deal-footer">
                    <div class="price-section">
                        ${originalPrice}
                        <span class="deal-price">Rs. ${deal.price}</span>
                    </div>
                    <button class="order-deal-btn" onclick="orderDeal('${deal.id}', '${deal.restaurant_id}')">
                        <i class="fas fa-shopping-cart"></i>
                        Order Now
                    </button>
                </div>
            </div>
        </div>
    `;
}

async function orderDeal(dealId, restaurantId) {
    try {
        // Find the deal
        const deal = allDeals.find(d => d.id === dealId);
        if (!deal) {
            showNotification('Deal not found!', 'error');
            return;
        }

        // Add deal to cart
        addToCart({
            id: deal.id,
            name: deal.name,
            description: deal.description,
            price: deal.price,
            image_url: deal.image_url,
            restaurant_name: deal.restaurants?.name || 'Restaurant',
            restaurant_id: deal.restaurant_id,
            quantity: 1,
            type: 'deal'
        });

        showNotification(`${deal.name} added to cart!`);

        // Update cart badge
        updateCartBadge();

    } catch (error) {
        console.error('Error ordering deal:', error);
        showNotification('Failed to add deal to cart. Please try again.', 'error');
    }
}

// Add to cart function
function addToCart(item) {
    let cart = JSON.parse(localStorage.getItem('torbaaz_cart')) || [];

    // Check if item already exists in cart
    const existingItemIndex = cart.findIndex(cartItem => cartItem.id === item.id);

    if (existingItemIndex !== -1) {
        // Update quantity if item exists
        cart[existingItemIndex].quantity += item.quantity;
    } else {
        // Add new item to cart
        cart.push(item);
    }

    // Save to localStorage
    localStorage.setItem('torbaaz_cart', JSON.stringify(cart));
}

// Update cart badge
function updateCartBadge() {
    const cart = JSON.parse(localStorage.getItem('torbaaz_cart')) || [];
    const totalItems = cart.reduce((total, item) => total + item.quantity, 0);

    const cartBadge = document.querySelector('.cart-badge');
    if (cartBadge) {
        cartBadge.textContent = totalItems;
        cartBadge.style.display = totalItems > 0 ? 'block' : 'none';
    }
}

async function searchDeals() {
    const searchInput = document.querySelector('.search-bar input');
    const query = searchInput.value.trim().toLowerCase();
    
    if (!query) {
        filteredDeals = [...allDeals];
        sortAndDisplayDeals();
        return;
    }
    
    filteredDeals = allDeals.filter(deal => 
        deal.name.toLowerCase().includes(query) ||
        deal.description.toLowerCase().includes(query) ||
        (deal.restaurants?.name || '').toLowerCase().includes(query)
    );
    
    sortAndDisplayDeals();
}

// Utility functions
function showLoading(container) {
    container.innerHTML = `
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p>Loading amazing deals...</p>
        </div>
    `;
}

function showError(container, message) {
    container.innerHTML = `
        <div class="error-state">
            <i class="fas fa-exclamation-circle"></i>
            <p>${message}</p>
            <button onclick="loadDeals()" class="retry-btn">Try Again</button>
        </div>
    `;
}

function showEmpty(container, message) {
    container.innerHTML = `
        <div class="empty-state">
            <i class="fas fa-tags"></i>
            <p>${message}</p>
            <button onclick="currentCategory='all'; filterAndDisplayDeals();" class="retry-btn">
                Show All Deals
            </button>
        </div>
    `;
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function showNotification(message) {
    const notification = document.createElement('div');
    notification.className = 'notification';
    notification.textContent = message;
    document.body.appendChild(notification);

    setTimeout(() => notification.classList.add('show'), 100);
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => notification.remove(), 300);
    }, 3000);
}
