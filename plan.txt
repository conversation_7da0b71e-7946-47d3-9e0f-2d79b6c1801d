I need you to complete our discussed plan @plan_for_torbaaz_web.txt and get the final objective as soon as possible so track the progress of our current situation and as an expert developer create an website that is basically derived from torbaaz food app ( which was in flutter) copy it and convert the code to node js frame work and language is java script and for styling we are using Html , css and so on 


so track current progress there is already an website that you have already made but the problem is its too basic and does not have real data but mock data

OBJECTIVE is to build an torbaaz food delivery website that has all key featues as in torbaaz-main @Torbaaz-main app and has AI Assistant that uses OpenAi Ai assistant 

the id of openAI Assistant is already given in the code base @plan_for_torbaaz_web.txt

Follow the instructions @instructions.pdf given in instructions pdf 

use MCP server if needed for supabase

use openAI API key that I gave you

see the past chats @Execute phase one for torbaaz web app @Food Items List from Torbaaz @Continue building the project after installation 

use the @ai_assistant_data.txt data for training ai assistant and using it to display food items on website instead of mock data 




follow the  rules in .cursor file

FOLLOW @instructions.pdf as your guide